<?php

namespace App\Filament\Pages\Auth;

use App\Models\Shops;
use App\Models\Subscriptions;
use App\Models\User;
use Filament\Auth\Pages\Register as BaseRegister;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class Register extends BaseRegister
{

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),

                TextInput::make('email')
                    ->email()
                    ->required()
                    ->maxLength(255),

                TextInput::make('password')
                    ->password()
                    ->required()
                    ->maxLength(255),

                TextInput::make('password_confirmation')
                    ->password()
                    ->same('password')
                    ->required(),

                // 🔹 Add your extra fields here
                TextInput::make('phone')
                    ->label('Phone Number')
                    ->tel()
                    ->required(),

                TextInput::make('address')
                    ->label('Shop Address')
                    ->required(),
            ]);
    }
    
    protected function handleRegistration(array $data): User
    {
        return DB::transaction(function () use ($data) {
            $plainPassword = $data['password'];

            // Only hash if it doesn't look like a bcrypt hash
            if (!str_starts_with($plainPassword, '$2y$')) {
                $plainPassword = Hash::make($plainPassword);
            }

            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => $plainPassword,
                'role' => 'Admin',
            ]);
            Shops::create([
                'user_id' => $user->id,
            ]);

            Subscriptions::create([
                'user_id' => $user->id,
            ]);

            return $user;
        });
    }
}
